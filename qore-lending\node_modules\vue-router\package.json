{"name": "vue-router", "version": "4.5.1", "main": "index.js", "unpkg": "dist/vue-router.global.js", "jsdelivr": "dist/vue-router.global.js", "module": "dist/vue-router.mjs", "types": "dist/vue-router.d.ts", "exports": {".": {"types": "./dist/vue-router.d.ts", "node": {"import": {"production": "./dist/vue-router.node.mjs", "development": "./dist/vue-router.node.mjs", "default": "./dist/vue-router.node.mjs"}, "require": {"production": "./dist/vue-router.prod.cjs", "development": "./dist/vue-router.cjs", "default": "./index.js"}}, "import": "./dist/vue-router.mjs", "require": "./index.js"}, "./dist/*": "./dist/*", "./vetur/*": "./vetur/*", "./package.json": "./package.json", "./auto-routes": {"types": "./vue-router-auto-routes.d.ts", "node": {"import": {"production": "./dist/vue-router.node.mjs", "development": "./dist/vue-router.node.mjs", "default": "./dist/vue-router.node.mjs"}, "require": {"production": "./dist/vue-router.prod.cjs", "development": "./dist/vue-router.cjs", "default": "./index.js"}}, "import": "./dist/vue-router.mjs", "require": "./index.js"}, "./auto": {"types": "./vue-router-auto.d.ts", "node": {"import": {"production": "./dist/vue-router.node.mjs", "development": "./dist/vue-router.node.mjs", "default": "./dist/vue-router.node.mjs"}, "require": {"production": "./dist/vue-router.prod.cjs", "development": "./dist/vue-router.cjs", "default": "./index.js"}}, "import": "./dist/vue-router.mjs", "require": "./index.js"}}, "sideEffects": false, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "funding": "https://github.com/sponsors/posva", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/vuejs/router.git"}, "bugs": {"url": "https://github.com/vuejs/router/issues"}, "homepage": "https://github.com/vuejs/router#readme", "files": ["index.js", "dist/*.{js,cjs,mjs}", "dist/vue-router.d.ts", "vue-router-auto.d.ts", "vue-router-auto-routes.d.ts", "vetur/tags.json", "vetur/attributes.json", "README.md"], "peerDependencies": {"vue": "^3.2.0"}, "vetur": {"tags": "vetur/tags.json", "attributes": "vetur/attributes.json"}, "dependencies": {"@vue/devtools-api": "^6.6.4"}, "devDependencies": {"@microsoft/api-extractor": "^7.48.0", "@rollup/plugin-alias": "^5.1.1", "@rollup/plugin-commonjs": "^25.0.8", "@rollup/plugin-node-resolve": "^15.3.0", "@rollup/plugin-replace": "^5.0.7", "@rollup/plugin-terser": "^0.4.4", "@types/jsdom": "^21.1.7", "@types/nightwatch": "^2.3.32", "@vitejs/plugin-vue": "^5.2.0", "@vue/compiler-sfc": "~3.5.13", "@vue/server-renderer": "~3.5.13", "@vue/test-utils": "^2.4.6", "browserstack-local": "^1.5.5", "chromedriver": "^131.0.1", "connect-history-api-fallback": "^1.6.0", "conventional-changelog-cli": "^2.2.2", "dotenv": "^16.4.5", "faked-promise": "^2.2.2", "geckodriver": "^4.5.1", "happy-dom": "^15.11.6", "nightwatch": "^2.6.25", "nightwatch-helpers": "^1.2.0", "rimraf": "^6.0.1", "rollup": "^3.29.5", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-typescript2": "^0.36.0", "vite": "^5.4.11", "vue": "~3.5.13"}, "scripts": {"dev": "vitest --ui", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s -r 1", "build": "rimraf dist && rollup -c rollup.config.mjs", "build:dts": "api-extractor run --local --verbose && tail -n +10 src/globalExtensions.ts >> dist/vue-router.d.ts", "build:playground": "vue-tsc --noEmit && vite build --config playground/vite.config.ts", "build:e2e": "vue-tsc --noEmit && vite build --config e2e/vite.config.mjs", "build:size": "pnpm run build && rollup -c size-checks/rollup.config.mjs", "dev:e2e": "vite --config e2e/vite.config.mjs", "test:types": "tsc --build tsconfig.json", "test:unit": "vitest --coverage run", "test": "pnpm run build && pnpm run build:dts && pnpm run test:types && pnpm run test:unit && pnpm run test:e2e", "test:e2e": "pnpm run test:e2e:headless", "test:e2e:headless": "node e2e/runner.mjs --env chrome-headless", "test:e2e:native": "node e2e/runner.mjs --env chrome", "test:e2e:ci": "node e2e/runner.mjs --env chrome-headless --retries 2", "test:e2e:bs": "node e2e/runner.mjs --local -e android5 --tag browserstack", "test:e2e:bs-test": "node e2e/runner.mjs --local --env browserstack.local_chrome --tag browserstack"}}