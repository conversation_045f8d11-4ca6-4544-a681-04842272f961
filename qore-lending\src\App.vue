<template>
  <div class="min-h-screen relative overflow-hidden bg-gradient-to-br from-slate-900 to-slate-800">
    <!-- Subtle background shapes -->
    <div class="absolute inset-0 pointer-events-none">
      <div class="absolute top-32 left-16 w-96 h-96 bg-gradient-to-tr from-slate-700/20 to-slate-800/20 rounded-full blur-3xl"></div>
      <div class="absolute bottom-32 right-24 w-80 h-80 bg-gradient-to-tr from-slate-700/20 to-slate-800/20 rounded-full blur-3xl"></div>
    </div>

    <!-- Particle background (subtle) -->
    <div class="absolute inset-0 opacity-20">
      <div
        v-for="i in 30"
        :key="i"
        class="absolute w-1 h-1 bg-slate-600 rounded-full animate-ping"
        :style="{
          left: Math.random() * 100 + '%',
          top: Math.random() * 100 + '%',
          animationDelay: Math.random() * 5 + 's',
          animationDuration: (3 + Math.random() * 3) + 's'
        }"
      ></div>
    </div>

    <div class="relative z-10 flex flex-col min-h-screen">
      <!-- Navbar -->
      <header class="p-6 bg-slate-900/70 backdrop-blur-md border-b border-slate-700">
        <nav class="flex justify-between items-center max-w-7xl mx-auto">
          <div class="flex items-center space-x-4">
            <div class="w-10 h-10 bg-gradient-to-r from-blue-900 to-slate-700 rounded-lg flex items-center justify-center">
              <span class="text-white font-bold text-xl">Q</span>
            </div>
            <h1 class="text-3xl font-extrabold text-white">
              Qore Lending
            </h1>
          </div>
          <button
            @click="handleInvestNow"
            class="px-6 py-2 bg-gradient-to-r from-blue-800 to-slate-700 text-white font-semibold rounded-full hover:scale-105 transition-all duration-300"
          >
            Invest Now
          </button>
        </nav>
      </header>

      <!-- Hero Section -->
      <main class="flex-1 flex flex-col justify-center items-center text-center px-6 py-20">
        <div class="mb-8 space-y-4">
          <h2 class="text-5xl sm:text-6xl md:text-7xl font-extrabold text-white leading-tight">
            Secure Your Financial Future
          </h2>
          <div class="text-2xl sm:text-3xl text-slate-300 font-medium">
            40% Annual Returns on Your Investment
          </div>
        </div>

        <div class="max-w-3xl mb-12">
          <p class="text-lg sm:text-xl text-slate-400 leading-relaxed bg-slate-800/50 backdrop-blur-md p-6 rounded-xl border border-slate-700">
            Qore Lending offers a secure and lucrative opportunity to grow your wealth.
            Our diversified lending operations provide business and personal loans
            that generate steady, attractive interest income—enabling us to deliver
            guaranteed 40% annual returns for our investors.
          </p>
        </div>

        <div class="flex flex-col sm:flex-row gap-6 mb-12">
          <button
            @click="handleInvestNow"
            class="px-10 py-4 bg-gradient-to-r from-blue-800 to-slate-700 text-white text-lg font-bold rounded-full hover:scale-105 hover:shadow-lg transition-all duration-300"
          >
            Start Investing
          </button>
          <button
            class="px-10 py-4 border-2 border-blue-700 text-blue-400 text-lg font-semibold rounded-full hover:bg-blue-700 hover:text-white transition-all duration-300"
          >
            Learn More
          </button>
        </div>

        <!-- Stats Section -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 w-full max-w-4xl">
          <div class="bg-slate-800/50 backdrop-blur-md border border-slate-700 rounded-xl p-6 hover:bg-slate-700/50 transition duration-300">
            <div class="text-3xl font-extrabold text-blue-400 mb-2">$50M+</div>
            <div class="text-slate-400">Total Investments</div>
          </div>
          <div class="bg-slate-800/50 backdrop-blur-md border border-slate-700 rounded-xl p-6 hover:bg-slate-700/50 transition duration-300">
            <div class="text-3xl font-extrabold text-blue-400 mb-2">99.8%</div>
            <div class="text-slate-400">Success Rate</div>
          </div>
          <div class="bg-slate-800/50 backdrop-blur-md border border-slate-700 rounded-xl p-6 hover:bg-slate-700/50 transition duration-300">
            <div class="text-3xl font-extrabold text-blue-400 mb-2">5,000+</div>
            <div class="text-slate-400">Happy Investors</div>
          </div>
        </div>
      </main>

      <!-- Footer -->
      <footer class="bg-slate-900/70 backdrop-blur-md border-t border-slate-700 text-center p-6">
        <div class="max-w-7xl mx-auto flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0 text-slate-500">
          <div>&copy; 2025 Qore Lending. All rights reserved.</div>
          <div class="flex space-x-6">
            <a href="#" class="hover:text-blue-400 transition-colors">Privacy Policy</a>
            <a href="#" class="hover:text-blue-400 transition-colors">Terms of Service</a>
            <a href="#" class="hover:text-blue-400 transition-colors">Contact Us</a>
          </div>
        </div>
      </footer>
    </div>
  </div>
</template>

<script>
export default {
  name: 'App',
  methods: {
    handleInvestNow() {
      // Simulated invest-now action
      alert("🚀 Let's get started with your investment journey!");
    },
  },
};
</script>

<style scoped>
/* Additional custom styles if needed */
</style>
